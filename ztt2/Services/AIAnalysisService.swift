//
//  AIAnalysisService.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import Foundation
import Network
import SwiftUI
import CoreData

/**
 * AI分析服务
 * 负责DeepSeek API调用、数据脱敏处理和权限验证
 */
@MainActor
class AIAnalysisService: ObservableObject {
    
    // MARK: - Properties
    
    private let baseURL = "https://api.deepseek.com"
    private let model = "deepseek-chat"
    private let keychainManager = KeychainManager.shared
    private let dataManager = DataManager.shared
    
    // 网络监控
    private let networkMonitor = NWPathMonitor()
    private let networkQueue = DispatchQueue(label: "NetworkMonitor")
    @Published var isNetworkAvailable = true
    
    // 请求状态
    @Published var isGenerating = false
    @Published var errorMessage: String?
    
    // 使用限制缓存
    private var usageCache: [String: AIAnalysisUsage] = [:]
    
    // MARK: - Initialization
    
    init() {
        startNetworkMonitoring()
        
        // 初始化时设置API密钥（如果还没有的话）
        if !keychainManager.hasAPIKey() {
            keychainManager.saveAPIKey("sk-eb2dc94c4f594097b7747421169b9110")
        }
    }
    
    deinit {
        networkMonitor.cancel()
    }
    
    // MARK: - Public Methods
    
    /**
     * 验证用户权限 - 用于页面显示权限检查
     */
    func validatePageAccess(for member: Member) -> AIAnalysisPermissionResult {
        // 检查会员等级
        guard let user = dataManager.currentUser, user.isAdvancedUser else {
            return .denied(
                reason: "AI分析功能需要高级会员权限",
                canUpgrade: true
            )
        }

        // 高级会员可以访问页面，即使数据不足
        return .allowed
    }

    /**
     * 验证生成报告权限 - 用于生成报告时的完整权限检查
     */
    func validateReportGeneration(for member: Member, reportType: AIReportType) -> AIAnalysisPermissionResult {
        // 检查会员等级
        guard let user = dataManager.currentUser, user.isAdvancedUser else {
            return .denied(
                reason: "AI分析功能需要高级会员权限",
                canUpgrade: true
            )
        }

        // 检查数据数量
        let dataCount: Int
        let errorMessage: String
        switch reportType {
        case .behaviorAnalysis:
            dataCount = member.sortedPointRecords.filter { !$0.isReversed }.count
            errorMessage = String(format: "ai_analysis.error.insufficient_point_records".localized, dataCount)
        case .growthReport:
            dataCount = member.sortedGrowthDiaries.count
            errorMessage = String(format: "ai_analysis.error.insufficient_growth_diaries".localized, dataCount)
        }

        guard dataCount >= 10 else {
            return .denied(reason: errorMessage)
        }

        // 检查网络连接
        guard isNetworkAvailable else {
            return .denied(reason: "网络连接不可用，请检查网络设置")
        }

        // 检查API密钥
        guard keychainManager.hasAPIKey() else {
            return .denied(reason: "API密钥未设置")
        }
        
        // 检查每日使用限制
        let usage = getTodayUsage(for: member)
        guard usage.canGenerate(reportType: reportType) else {
            return .denied(reason: "今日\(reportType.displayName)生成次数已用完（100次/天）")
        }

        return .allowed
    }

    /**
     * 兼容旧接口 - 使用生成报告权限检查
     */
    func validatePermissions(for member: Member, reportType: AIReportType) -> AIAnalysisPermissionResult {
        return validateReportGeneration(for: member, reportType: reportType)
    }

    /**
     * 生成AI分析报告
     */
    func generateReport(for member: Member, reportType: AIReportType) async throws -> AIAnalysisReport {
        // 验证权限
        let permissionResult = validateReportGeneration(for: member, reportType: reportType)
        guard permissionResult.isAllowed else {
            throw AIAnalysisError.permissionDenied
        }
        
        // 设置生成状态
        isGenerating = true
        errorMessage = nil
        
        defer {
            isGenerating = false
        }
        
        do {
            // 准备分析数据
            guard let analysisData = StudentAnalysisData.from(member: member, reportType: reportType) else {
                throw AIAnalysisError.insufficientData
            }
            
            // 执行API请求
            let report = try await performAPIRequest(data: analysisData, reportType: reportType, member: member)
            
            // 更新使用次数
            updateUsageCount(for: member, reportType: reportType)
            
            // 保存报告到CoreData
            saveReportToCoreData(report)
            
            return report
            
        } catch {
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    /**
     * 检查网络连接状态
     */
    func checkNetworkConnection() -> Bool {
        return isNetworkAvailable
    }
    
    /**
     * 获取今日使用情况
     */
    func getTodayUsage(for member: Member) -> AIAnalysisUsage {
        let today = Calendar.current.startOfDay(for: Date())
        let cacheKey = "\(member.id?.uuidString ?? "")-\(today.timeIntervalSince1970)"
        
        if let cached = usageCache[cacheKey] {
            return cached
        }
        
        // 从CoreData查询今日使用情况
        let behaviorCount = getReportCount(for: member, reportType: .behaviorAnalysis, date: today)
        let growthCount = getReportCount(for: member, reportType: .growthReport, date: today)
        
        let usage = AIAnalysisUsage(
            memberID: member.id ?? UUID(),
            date: today,
            behaviorAnalysisCount: behaviorCount,
            growthReportCount: growthCount
        )
        
        usageCache[cacheKey] = usage
        return usage
    }
    
    // MARK: - Private Methods
    
    /**
     * 开始网络监控
     */
    private func startNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.isNetworkAvailable = path.status == .satisfied
            }
        }
        networkMonitor.start(queue: networkQueue)
    }
    
    /**
     * 执行API请求
     */
    private func performAPIRequest(data: StudentAnalysisData, reportType: AIReportType, member: Member) async throws -> AIAnalysisReport {
        guard let apiKey = keychainManager.getAPIKey() else {
            throw AIAnalysisError.apiKeyMissing
        }
        
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw AIAnalysisError.apiError("无效的API URL")
        }
        
        // 构建请求
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("DeepSeek-iOS-Client/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 90.0
        
        // 构建请求体
        let requestBody = buildRequestBody(data: data, reportType: reportType)
        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)
        
        // 发送请求
        let (responseData, response) = try await URLSession.shared.data(for: request)
        
        // 处理响应
        guard let httpResponse = response as? HTTPURLResponse else {
            throw AIAnalysisError.invalidResponse
        }
        
        guard httpResponse.statusCode == 200 else {
            let errorMessage = String(data: responseData, encoding: .utf8) ?? "未知错误"
            throw AIAnalysisError.apiError("HTTP \(httpResponse.statusCode): \(errorMessage)")
        }
        
        // 解析响应
        let deepSeekResponse = try JSONDecoder().decode(DeepSeekResponse.self, from: responseData)
        
        guard let choice = deepSeekResponse.choices.first,
              !choice.message.content.isEmpty else {
            throw AIAnalysisError.invalidResponse
        }
        
        // 创建报告
        let report = AIAnalysisReport(
            reportType: reportType,
            memberName: member.name ?? "未知",
            memberRole: member.role ?? "孩子",
            memberAge: member.age,
            content: choice.message.content
        )
        
        return report
    }
    
    /**
     * 构建API请求体
     */
    private func buildRequestBody(data: StudentAnalysisData, reportType: AIReportType) -> [String: Any] {
        let prompt = buildPrompt(data: data, reportType: reportType)
        
        return [
            "model": model,
            "messages": [
                [
                    "role": "user",
                    "content": prompt
                ]
            ],
            "max_tokens": 4000,
            "temperature": 0.7,
            "stream": false
        ]
    }
    
    /**
     * 构建分析提示词
     */
    private func buildPrompt(data: StudentAnalysisData, reportType: AIReportType) -> String {
        switch reportType {
        case .behaviorAnalysis:
            return buildBehaviorAnalysisPrompt(data: data)
        case .growthReport:
            return buildGrowthReportPrompt(data: data)
        }
    }

    /**
     * 构建行为分析报告的提示词
     */
    private func buildBehaviorAnalysisPrompt(data: StudentAnalysisData) -> String {
        let recordsText = data.pointRecords?.map { record in
            "- \(record.formattedTimestamp) \(record.scoreChange) \(record.reason)"
        }.joined(separator: "\n") ?? ""

        return """
你是一位拥有15年以上经验的专业儿童行为分析师和家庭教育顾问，专门研究3-18岁儿童的行为模式、心理发展和教育指导。你具备儿童心理学、发展心理学和行为分析学的专业背景，擅长通过行为数据分析孩子的成长状态，并为家长提供科学、实用的教育建议。

请基于家庭积分管理系统中记录的孩子行为数据，运用专业的行为分析理论和方法，为家长生成一份客观、深入且具有指导价值的行为分析报告。

**分析对象信息：**
- 角色：\(data.role)
- 年龄：\(data.age)岁
- 数据来源：家庭积分管理系统的行为记录

**行为记录数据：**
以下是按时间顺序排列的孩子行为记录，包含加分（正向行为）和扣分（需要改进的行为）：
\(recordsText)

数据格式说明：
- 时间戳 分数变化 行为描述
- 正分表示值得鼓励的行为，负分表示需要改进的行为
- 请重点关注行为的时间分布、频率变化和行为类型

**分析要求：**
1. 运用行为分析学理论，客观分析数据中反映的行为模式
2. 结合该年龄段儿童的发展特点，提供专业的解读
3. 语言要专业但易懂，避免过于学术化的表述
4. 建议要具体可操作，符合中国家庭的实际情况
5. 保持积极正面的语调，重视孩子的进步和潜力

请生成包含以下内容的分析报告（严格使用Markdown格式）：

# \(data.name)的行为分析报告

## 行为趋势分析
分析最近的行为变化趋势，识别积极和消极的行为模式。

## 行为模式识别
识别孩子的行为规律，如特定时间段的行为特点。

## 进步亮点总结
突出孩子近期的积极变化和值得表扬的地方。

## 注意事项提醒
基于扣分记录，提醒家长需要关注的行为问题。

## 个性化教育建议
结合近期表现，提供具体的教育指导建议。

## 激励建议
针对孩子当前表现，建议合适的奖励和激励方式。

## 推荐加/扣分规则
基于分析结果，推荐3条新的加分或扣分规则：
1. [规则名称] +/-[分数]分 - [使用场景说明]
2. [规则名称] +/-[分数]分 - [使用场景说明]
3. [规则名称] +/-[分数]分 - [使用场景说明]

请确保分析客观、建议实用，语言温和且具有指导性。
"""
    }

    /**
     * 构建成长报告的提示词
     */
    private func buildGrowthReportPrompt(data: StudentAnalysisData) -> String {
        let diaryText = data.diaryEntries?.map { entry in
            "- \(entry.formattedTimestamp) \(entry.content)"
        }.joined(separator: "\n") ?? ""

        return """
你是一位拥有20年以上实践经验的资深家庭教育专家和儿童心理咨询师，具备教育学博士学位和国家二级心理咨询师资质。你专门研究亲子关系、儿童情绪发展、家庭教育方法，并长期为数千个家庭提供个性化的教育指导服务。你的专业特长包括：儿童情绪管理、亲子沟通技巧、家庭教育策略制定、儿童阅读指导等。

你现在需要基于家长记录的孩子成长日记，运用专业的儿童心理学理论和家庭教育实践经验，为家长提供一份深度的、个性化的成长分析报告。请以一位经验丰富的教育专家与家长面对面咨询的语调，提供温暖、专业且实用的分析和建议。

**咨询对象信息：**
- 角色：\(data.role)
- 年龄：\(data.age)岁
- 数据来源：家长观察记录的成长日记

**成长观察记录：**
以下是家长记录的孩子日常表现和成长片段，请仔细分析其中反映的孩子情绪状态、行为特点、兴趣发展、社交能力等方面的信息：
\(diaryText)

记录格式说明：
- 每条记录包含时间和家长的观察描述
- 内容涵盖孩子的日常行为、情绪表现、学习状态、社交互动等
- 请特别关注情绪变化的时间线、行为模式的规律性

**专业分析要求：**
1. 运用儿童发展心理学理论，深入分析孩子的成长状态
2. 结合该年龄段的发展特点和里程碑，提供专业解读
3. 识别潜在的教育机会和需要关注的问题
4. 提供具体可操作的家庭教育建议和亲子互动策略
5. 语言要专业但温暖，就像面对面咨询一样亲切
6. 重视家长的观察和记录，给予积极的反馈和指导

请以家庭教育专家的专业视角，生成包含以下内容的成长报告（严格使用Markdown格式）：

# \(data.name)的成长报告

## 情绪变化分析
深入分析孩子的情绪状态变化，识别情绪模式和可能的触发因素。

## 成长观察与洞察
从专业角度观察孩子的成长表现，发现成长亮点和需要关注的方面。

## 亲子沟通建议
基于日记内容，提供具体的沟通策略和技巧，帮助家长更好地理解和回应孩子。

## 家庭教育建议
提供专业的教育指导，包括：
- 针对当前阶段的教育重点
- 具体的教育方法和策略
- 家庭环境营造建议
- 日常互动改善建议

## 推荐书籍
根据孩子的年龄和日记中反映的特点，推荐2本适合的书籍：
- 《书名》 - 推荐理由和如何使用
- 《书名》 - 推荐理由和如何使用

## 专家提醒
作为家庭教育专家，我想特别提醒您关注的几个方面：
[基于日记内容，提供专业的提醒和建议，如发现孩子可能处于某个发展阶段的特殊表现]

请用温暖、专业且易懂的语言，就像一位经验丰富的教育专家在与家长面对面交流一样。分析要深入但不过于学术化，建议要具体可操作。
"""
    }

    /**
     * 更新使用次数
     */
    private func updateUsageCount(for member: Member, reportType: AIReportType) {
        let today = Calendar.current.startOfDay(for: Date())
        let cacheKey = "\(member.id?.uuidString ?? "")-\(today.timeIntervalSince1970)"

        var usage = getTodayUsage(for: member)

        switch reportType {
        case .behaviorAnalysis:
            usage = AIAnalysisUsage(
                memberID: usage.memberID,
                date: usage.date,
                behaviorAnalysisCount: usage.behaviorAnalysisCount + 1,
                growthReportCount: usage.growthReportCount
            )
        case .growthReport:
            usage = AIAnalysisUsage(
                memberID: usage.memberID,
                date: usage.date,
                behaviorAnalysisCount: usage.behaviorAnalysisCount,
                growthReportCount: usage.growthReportCount + 1
            )
        }

        usageCache[cacheKey] = usage
    }

    /**
     * 从CoreData查询报告数量
     */
    private func getReportCount(for member: Member, reportType: AIReportType, date: Date) -> Int {
        let context = dataManager.persistenceController.container.viewContext
        let request: NSFetchRequest<AIReport> = AIReport.fetchRequest()

        let calendar = Calendar.current
        let startOfDay = calendar.startOfDay(for: date)
        let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) ?? Date()

        let reportTypeString = reportType == .behaviorAnalysis ? "analysis" : "growth"

        request.predicate = NSPredicate(format: "member == %@ AND reportType == %@ AND createdAt >= %@ AND createdAt < %@",
                                      member, reportTypeString, startOfDay as NSDate, endOfDay as NSDate)

        do {
            let reports = try context.fetch(request)
            return reports.count
        } catch {
            print("❌ 查询AI报告数量失败: \(error)")
            return 0
        }
    }

    /**
     * 保存报告到CoreData
     */
    private func saveReportToCoreData(_ report: AIAnalysisReport) {
        let context = dataManager.persistenceController.container.viewContext

        // 查找对应的成员
        let memberRequest: NSFetchRequest<Member> = Member.fetchRequest()
        memberRequest.predicate = NSPredicate(format: "name == %@", report.memberName)

        do {
            let members = try context.fetch(memberRequest)
            guard let member = members.first else {
                print("❌ 未找到成员: \(report.memberName)")
                return
            }

            // 创建AI报告实体
            let aiReport = AIReport(context: context)
            aiReport.id = report.id
            aiReport.title = "\(report.memberName)的\(report.reportType.displayName)"
            aiReport.content = report.content
            aiReport.reportType = report.reportType == .behaviorAnalysis ? "analysis" : "growth"
            aiReport.createdAt = report.createdAt
            aiReport.member = member

            // 设置数据摘要信息
            if report.reportType == .behaviorAnalysis {
                let validRecords = member.sortedPointRecords.filter { !$0.isReversed }
                aiReport.totalRecords = Int32(validRecords.count)
                aiReport.positiveRecords = Int32(validRecords.filter { $0.value > 0 }.count)
                aiReport.negativeRecords = Int32(validRecords.filter { $0.value < 0 }.count)
                aiReport.inputDataSummary = "基于\(validRecords.count)条行为记录的分析"
            } else {
                let diaryCount = member.sortedGrowthDiaries.count
                aiReport.totalRecords = Int32(diaryCount)
                aiReport.inputDataSummary = "基于\(diaryCount)条成长日记的分析"
            }

            // 保存到CoreData
            try context.save()
            print("✅ AI分析报告已保存到CoreData: \(report.reportType.displayName)")

            // 手动触发CloudKit同步，确保AI分析报告能够同步到其他设备
            print("📊 AI分析报告已保存，触发CloudKit同步...")
            Task {
                await iCloudSyncManager.shared.triggerManualSync()
            }

        } catch {
            print("❌ 保存AI分析报告失败: \(error)")
        }
    }

    /**
     * 获取成员的历史报告
     */
    func getHistoryReports(for member: Member, reportType: AIReportType? = nil) -> [AIReport] {
        let context = dataManager.persistenceController.container.viewContext
        let request: NSFetchRequest<AIReport> = AIReport.fetchRequest()

        var predicates: [NSPredicate] = [NSPredicate(format: "member == %@", member)]

        if let reportType = reportType {
            let reportTypeString = reportType == .behaviorAnalysis ? "analysis" : "growth"
            predicates.append(NSPredicate(format: "reportType == %@", reportTypeString))
        }

        request.predicate = NSCompoundPredicate(andPredicateWithSubpredicates: predicates)
        request.sortDescriptors = [NSSortDescriptor(key: "createdAt", ascending: false)]

        do {
            return try context.fetch(request)
        } catch {
            print("❌ 查询历史报告失败: \(error)")
            return []
        }
    }
}
