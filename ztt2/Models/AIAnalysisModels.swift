//
//  AIAnalysisModels.swift
//  ztt2
//
//  Created by Augment Agent on 2025/8/1.
//

import Foundation

// MARK: - AI分析报告类型

/**
 * AI分析报告类型
 */
enum AIReportType: String, CaseIterable {
    case behaviorAnalysis = "behavior_analysis"  // 行为分析报告
    case growthReport = "growth_report"          // 成长报告
    
    var displayName: String {
        switch self {
        case .behaviorAnalysis:
            return "行为分析报告"
        case .growthReport:
            return "成长报告"
        }
    }
}

// MARK: - AI分析权限结果

/**
 * AI分析权限验证结果
 */
enum AIAnalysisPermissionResult {
    case allowed
    case denied(reason: String, canUpgrade: Bool = false)
    
    var isAllowed: Bool {
        switch self {
        case .allowed:
            return true
        case .denied:
            return false
        }
    }
    
    var errorMessage: String? {
        switch self {
        case .allowed:
            return nil
        case .denied(let reason, _):
            return reason
        }
    }
    
    var canUpgrade: Bool {
        switch self {
        case .allowed:
            return false
        case .denied(_, let canUpgrade):
            return canUpgrade
        }
    }
}

// MARK: - AI分析错误类型

/**
 * AI分析服务错误类型
 */
enum AIAnalysisError: Error, LocalizedError {
    case networkError(Error)
    case apiError(String)
    case invalidResponse
    case insufficientData
    case permissionDenied
    case rateLimitExceeded
    case apiKeyMissing
    case apiKeyInvalid
    
    var errorDescription: String? {
        switch self {
        case .networkError(let error):
            return "网络连接错误: \(error.localizedDescription)"
        case .apiError(let message):
            return "API错误: \(message)"
        case .invalidResponse:
            return "无效的API响应"
        case .insufficientData:
            return "数据不足，无法生成分析报告"
        case .permissionDenied:
            return "权限不足，请升级会员"
        case .rateLimitExceeded:
            return "今日分析次数已用完"
        case .apiKeyMissing:
            return "API密钥未设置"
        case .apiKeyInvalid:
            return "API密钥无效"
        }
    }
}

// MARK: - 学生分析数据

/**
 * 用于AI分析的学生数据
 */
struct StudentAnalysisData {
    let role: String           // 角色（儿子/女儿）
    let age: Int              // 年龄
    let pointRecords: [PointRecordData]?  // 积分记录（用于行为分析）
    let diaryEntries: [DiaryEntryData]?   // 日记记录（用于成长报告）
    
    /**
     * 从Member对象创建分析数据
     */
    static func from(member: Member, reportType: AIReportType) -> StudentAnalysisData? {
        // 计算年龄
        let age = member.age
        
        // 脱敏处理：使用角色而不是真实姓名
        let role = member.role ?? "孩子"
        
        switch reportType {
        case .behaviorAnalysis:
            // 获取积分记录
            let validRecords = member.sortedPointRecords.filter { !$0.isReversed }
            guard validRecords.count >= 10 else { return nil }
            
            let pointRecords = validRecords.map { record in
                PointRecordData(
                    reason: record.reason ?? "未知原因",
                    value: record.value,
                    timestamp: record.timestamp ?? Date()
                )
            }
            
            return StudentAnalysisData(
                role: role,
                age: age,
                pointRecords: pointRecords,
                diaryEntries: nil
            )
            
        case .growthReport:
            // 获取成长日记
            let diaryEntries = member.sortedGrowthDiaries
            guard diaryEntries.count >= 10 else { return nil }
            
            let diaryData = diaryEntries.map { diary in
                DiaryEntryData(
                    content: diary.content ?? "",
                    timestamp: diary.timestamp ?? Date()
                )
            }
            
            return StudentAnalysisData(
                role: role,
                age: age,
                pointRecords: nil,
                diaryEntries: diaryData
            )
        }
    }
}

/**
 * 积分记录数据
 */
struct PointRecordData {
    let reason: String
    let value: Int32
    let timestamp: Date
    
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: timestamp)
    }
    
    var scoreChange: String {
        return value >= 0 ? "+\(value)分" : "\(value)分"
    }
}

/**
 * 日记条目数据
 */
struct DiaryEntryData {
    let content: String
    let timestamp: Date
    
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        return formatter.string(from: timestamp)
    }
}

// MARK: - AI分析报告

/**
 * AI生成的分析报告
 */
struct AIAnalysisReport {
    let id: UUID
    let reportType: AIReportType
    let memberName: String
    let memberRole: String
    let memberAge: Int
    let content: String        // Markdown格式的报告内容
    let createdAt: Date
    
    init(reportType: AIReportType, memberName: String, memberRole: String, memberAge: Int, content: String) {
        self.id = UUID()
        self.reportType = reportType
        self.memberName = memberName
        self.memberRole = memberRole
        self.memberAge = memberAge
        self.content = content
        self.createdAt = Date()
    }
}

// MARK: - 使用限制数据

/**
 * AI分析使用限制数据
 */
struct AIAnalysisUsage {
    let memberID: UUID
    let date: Date
    let behaviorAnalysisCount: Int
    let growthReportCount: Int
    
    /**
     * 检查是否可以生成指定类型的报告
     */
    func canGenerate(reportType: AIReportType) -> Bool {
        switch reportType {
        case .behaviorAnalysis:
            return behaviorAnalysisCount < 100
        case .growthReport:
            return growthReportCount < 100
        }
    }

    /**
     * 获取剩余次数
     */
    func remainingCount(for reportType: AIReportType) -> Int {
        switch reportType {
        case .behaviorAnalysis:
            return max(0, 100 - behaviorAnalysisCount)
        case .growthReport:
            return max(0, 100 - growthReportCount)
        }
    }
}

// MARK: - DeepSeek API 请求/响应模型

/**
 * DeepSeek API请求模型
 */
struct DeepSeekRequest: Codable {
    let model: String
    let messages: [DeepSeekMessage]
    let maxTokens: Int
    let temperature: Double
    let stream: Bool
    
    enum CodingKeys: String, CodingKey {
        case model
        case messages
        case maxTokens = "max_tokens"
        case temperature
        case stream
    }
}

/**
 * DeepSeek消息模型
 */
struct DeepSeekMessage: Codable {
    let role: String
    let content: String
}

/**
 * DeepSeek API响应模型
 */
struct DeepSeekResponse: Codable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [DeepSeekChoice]
    let usage: DeepSeekUsage?
}

/**
 * DeepSeek选择模型
 */
struct DeepSeekChoice: Codable {
    let index: Int
    let message: DeepSeekMessage
    let finishReason: String?
    
    enum CodingKeys: String, CodingKey {
        case index
        case message
        case finishReason = "finish_reason"
    }
}

/**
 * DeepSeek使用统计模型
 */
struct DeepSeekUsage: Codable {
    let promptTokens: Int
    let completionTokens: Int
    let totalTokens: Int
    
    enum CodingKeys: String, CodingKey {
        case promptTokens = "prompt_tokens"
        case completionTokens = "completion_tokens"
        case totalTokens = "total_tokens"
    }
}
